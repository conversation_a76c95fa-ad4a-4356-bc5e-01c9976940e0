clc;
clear;
close all

% period = 168;
period = 24;
t = (1:8760)';
y = readmatrix('P_wphl.xlsx', 'Sheet', 'Sheet1');

% 执行STL分解（趋势 + 季节性 + 残差才能还原到原始值）。
[trend, seasonal, residual,h] = stl_loess(y, period, 3);

% % 保存趋势分量
%xlswrite('trend.xlsx', trend);

% 可视化验证    单纯stl分解情况
% close all
figure
subplot(4,1,1), plot(t, y), title('原始数据')
subplot(4,1,2), plot(t, trend), title('趋势分量')
subplot(4,1,3), plot(t, seasonal), title('周期分量')
subplot(4,1,4), plot(t, residual), title('残差')


% 假设已获得趋势分量trend（列向量）和原始时间序列
% 应用图示公式(32)的权重
rho = bisquare_weight(residual./h); % 使用先前定义的权重函数
weighted_trend = trend .* rho; 

% % 计算完整周数
% n = length(weighted_trend);
% num_weeks = floor(n/period); % 仅处理完整周
% % 执行周聚合
% weekly_sum = zeros(num_weeks, 1);
% for i = 1:num_weeks
%     idx_range = (i-1)*period+1 : i*period;
%     weekly_sum(i) = sum(weighted_trend(idx_range), 'omitnan');
% end

% 计算完整天数
n = length(weighted_trend);
num_days = floor(n/period); % 仅处理完整周
% 执行日聚合
daily_sum = zeros(num_days,1);
for i = 1:num_days
    idx_range = (i-1)*period+1 : i*period;
    daily_sum(i) = sum(weighted_trend(idx_range), 'omitnan');
end


% 验证输出
disp(table( daily_sum, 'VariableNames', {'累计值'}))
%可视化验证
figure
subplot(2,1,1)
plot(trend') 
title('原始趋势分量 ')

subplot(2,1,2)
bar( daily_sum', 'FaceColor', [0.2 0.6 0.8], 'BarWidth', 0.8)
title('氢储能SOC存放情况')
xlabel('时间/day')
ylabel('能量/MW')


% ququshi = y - trend;%周期+残差
% writematrix(ququshi, 'ququshi.xlsx'); 