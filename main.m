   clc; clear; close all;

%% 1. 参数设置
fprintf('正在初始化STL分解参数...\n');

% 基本参数 - 用户可根据需要修改
period = 24;                    % 周期长度，根据数据特点调整
n_outer = 6;                    % 外循环次数，增加稳健性
n_inner = 2;                    % 内循环次数

% LOESS平滑参数
trend_window = ceil(1.5 * period);     % 趋势平滑窗口
seasonal_window = 7;                   % 季节性平滑窗口

%% 2. 数据读取和预处理
fprintf('正在读取数据...\n');

try
    % 读取数据
    data = readmatrix('net load.xlsx', 'Sheet', 'Sheet1');
    
    % 检查数据
    if isempty(data)
        error('数据文件为空或读取失败');
    end
    
    % 确保数据为列向量
    if size(data, 2) > 1
        data = data(:, 1);  % 取第一列
    end
    
    n_points = length(data);
    time_vector = (1:n_points)';
    
    fprintf('成功读取 %d 个数据点\n', n_points);
    fprintf('数据范围: %.2f ~ %.2f\n', min(data), max(data));
    
catch ME
    error('数据读取失败: %s', ME.message);
end

%% 3. STL分解主程序
fprintf('开始STL分解...\n');

% 初始化分量
trend = zeros(n_points, 1);
seasonal = zeros(n_points, 1);
residual = zeros(n_points, 1);
weights = ones(n_points, 1);  % 初始权重

% 外循环：提高稳健性
for outer_iter = 1:n_outer
    fprintf('外循环 %d/%d\n', outer_iter, n_outer);
    
    % 内循环：STL分解
    for inner_iter = 1:n_inner
        % 步骤1：季节性分量提取
        detrended = data - trend;
        seasonal = extract_seasonal_component(detrended, period, seasonal_window, weights);
        
        % 步骤2：趋势分量提取
        deseasonalized = data - seasonal;
        trend = loess_smooth(deseasonalized, trend_window, 1, weights);
    end
    
    % 计算残差
    residual = data - trend - seasonal;
    
    % 更新稳健权重（基于残差）
    if outer_iter < n_outer
        mad_residual = median(abs(residual - median(residual)));
        h = 6 * mad_residual;  % 稳健尺度估计
        if h > 0
            weights = bisquare_weights(residual / h);
        end
    end
end

%% 4. 后处理
fprintf('进行后处理...\n');

% 最终趋势平滑
trend = loess_smooth(trend, trend_window, 1, weights);

% 计算去趋势分量（季节性 + 残差）
detrended = data - trend;

% 验证分解质量
reconstruction = trend + detrended;
max_error = max(abs(data - reconstruction));
fprintf('分解精度检验 - 最大重构误差: %.6f\n', max_error);

%% 5. 结果可视化
fprintf('生成可视化结果...\n');

% 主分解图
figure('Name', 'STL分解结果', 'Position', [100, 100, 1200, 600]);

subplot(3, 1, 1);
plot(time_vector, data, 'Color', [0.2 0.6 0.8], 'LineWidth', 1);
title('原始数据', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('数值');
grid on;
legend('原始数据', 'Location', 'best');

subplot(3, 1, 2);
plot(time_vector, trend, 'Color', [0.2 0.6 0.8], 'LineWidth', 1.5);
title('趋势分量', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('数值');
grid on;
legend('趋势', 'Location', 'best');

subplot(3, 1, 3);
plot(time_vector, detrended, 'Color', [0.2 0.6 0.8], 'LineWidth', 1);
title('残差', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('数值');
xlabel('时间');
grid on;
legend('去趋势', 'Location', 'best');

% 24小时日内模式分析
figure('Name', '24小时日内净负荷模式分析', 'Position', [200, 200, 1000, 600]);

% 计算典型日模式（使用去趋势分量）
daily_period = 24;  % 24小时日周期
n_days = floor(n_points / daily_period);
daily_pattern = zeros(daily_period, 1);
daily_pattern_count = zeros(daily_period, 1);

% 对每个小时位置计算平均值
for hour = 1:daily_period
    hour_indices = hour:daily_period:n_days*daily_period;
    if ~isempty(hour_indices) && hour_indices(end) <= length(detrended)
        daily_pattern(hour) = mean(detrended(hour_indices));
        daily_pattern_count(hour) = length(hour_indices);
    end
end

subplot(2, 1, 1);
plot(1:daily_period, daily_pattern, 'Color', [0.2 0.6 0.8], 'LineWidth', 2, 'Marker', 'o', 'MarkerSize', 4, 'MarkerFaceColor', [0.2 0.6 0.8]);
title('典型日内净负荷变化模式（24小时）', 'FontSize', 12, 'FontWeight', 'bold');
xlabel('小时');
ylabel('净负荷偏差');
grid on;
xlim([1, daily_period]);
xticks(1:4:daily_period);
xticklabels({'1', '5', '9', '13', '17', '21'});

% 趋势变化分析
subplot(2, 1, 2);
plot(time_vector/24, trend, 'Color', [0.2 0.6 0.8], 'LineWidth', 2);
title('净负荷长期趋势变化', 'FontSize', 12, 'FontWeight', 'bold');
xlabel('时间 (天)');
ylabel('净负荷');
grid on;
 
%% 6. 统计分析
fprintf('\n=== STL分解统计结果 ===\n');
fprintf('原始数据方差: %.2f\n', var(data));
fprintf('趋势分量方差: %.2f (%.1f%%)\n', var(trend), var(trend)/var(data)*100);
fprintf('去趋势分量方差: %.2f (%.1f%%)\n', var(detrended), var(detrended)/var(data)*100);

fprintf('\n=== 24小时日内模式分析 ===\n');
[max_val, max_hour] = max(daily_pattern);
[min_val, min_hour] = min(daily_pattern);
fprintf('日内净负荷最高点: %d时，偏差值 %.2f\n', max_hour, max_val);
fprintf('日内净负荷最低点: %d时，偏差值 %.2f\n', min_hour, min_val);
fprintf('日内净负荷波动幅度: %.2f\n', max_val - min_val);
fprintf('分析天数: %d天\n', n_days);

%% 氢储能系统分析
fprintf('\n=== 氢储能系统运行分析 ===\n');

% 氢储能系统效率参数
electrolyzer_efficiency = 0.8;    % 电解槽效率
fuel_cell_efficiency = 0.6;       % 燃料电池效率

% 根据趋势分量计算氢储能功率和储量变化
hydrogen_power = zeros(size(trend));        % 氢储能功率（用氢为正，制氢为负）
hydrogen_storage_change = zeros(size(trend)); % 氢储量变化（制氢为正，用氢为负）
hydrogen_type = zeros(size(trend));        % 1为用氢，-1为制氢，0为停机

for i = 1:length(trend)
    if trend(i) > 0
        % 净负荷为正，用氢（燃料电池工作）
        hydrogen_power(i) = trend(i) * fuel_cell_efficiency;  % 用氢功率（正值）
        hydrogen_storage_change(i) = -trend(i) * fuel_cell_efficiency;  % 储量减少（负值）
        hydrogen_type(i) = 1;  % 用氢模式
    elseif trend(i) < 0
        % 净负荷为负，制氢（电解槽工作）
        hydrogen_power(i) = trend(i) * electrolyzer_efficiency;  % 制氢功率（负值）
        hydrogen_storage_change(i) = -trend(i) * electrolyzer_efficiency;  % 储量增加（正值）
        hydrogen_type(i) = -1;  % 制氢模式
    else
        % 净负荷为零，停机
        hydrogen_power(i) = 0;
        hydrogen_storage_change(i) = 0;
        hydrogen_type(i) = 0;
    end
end

% 统计氢储能运行情况
total_hydrogen_release = sum(hydrogen_power(hydrogen_power > 0));  % 总用氢量
total_hydrogen_storage = abs(sum(hydrogen_power(hydrogen_power < 0)));  % 总制氢量
release_hours = sum(hydrogen_type == 1);  % 用氢小时数
storage_hours = sum(hydrogen_type == -1);  % 制氢小时数
idle_hours = sum(hydrogen_type == 0);  % 停机小时数

fprintf('用氢总量: %.2f MWh\n', total_hydrogen_release);
fprintf('制氢总量: %.2f MWh\n', total_hydrogen_storage);
fprintf('用氢时间: %d 小时 (%.1f%%)\n', release_hours, release_hours/length(trend)*100);
fprintf('制氢时间: %d 小时 (%.1f%%)\n', storage_hours, storage_hours/length(trend)*100);
fprintf('停机时间: %d 小时 (%.1f%%)\n', idle_hours, idle_hours/length(trend)*100);

% 计算累积氢储量（考虑物理约束）
hydrogen_cumsum_raw = cumsum(hydrogen_storage_change);
min_raw = min(hydrogen_cumsum_raw);

% 设定初始氢储量，确保存量始终非负
if min_raw < 0
    initial_storage = abs(min_raw) + 1000;  % 增加1000MWh作为安全余量
else
    initial_storage = 1000;  % 默认初始存量1000MWh
end

hydrogen_cumsum = hydrogen_cumsum_raw + initial_storage;

fprintf('\n=== 氢储罐容量需求 ===\n');
max_storage = max(hydrogen_cumsum);
min_storage = min(hydrogen_cumsum);
required_capacity = max_storage - min_storage;
fprintf('初始氢储量: %.2f MWh\n', initial_storage);
fprintf('最大储氢量: %.2f MWh\n', max_storage);
fprintf('最小储氢量: %.2f MWh\n', min_storage);
fprintf('所需储罐容量: %.2f MWh\n', required_capacity);

% 计算日级别统计（用于图表显示）
daily_period = 24;
n_complete_days = floor(length(hydrogen_power) / daily_period);
daily_hydrogen_power = zeros(n_complete_days, 1);
for day = 1:n_complete_days
    day_start = (day - 1) * daily_period + 1;
    day_end = day * daily_period;
    daily_hydrogen_power(day) = sum(hydrogen_power(day_start:day_end));
end

fprintf('\n=== 日级别氢储能统计 ===\n');
net_hydrogen_days = sum(daily_hydrogen_power > 0);  % 净用氢天数
net_storage_days = sum(daily_hydrogen_power < 0);   % 净制氢天数
balanced_days = sum(abs(daily_hydrogen_power) < 1); % 平衡天数
fprintf('净用氢天数: %d 天 (%.1f%%)\n', net_hydrogen_days, net_hydrogen_days/n_complete_days*100);
fprintf('净制氢天数: %d 天 (%.1f%%)\n', net_storage_days, net_storage_days/n_complete_days*100);
fprintf('停工天数: %d 天 (%.1f%%)\n', balanced_days, balanced_days/n_complete_days*100);

% 绘制氢储能系统运行图
figure('Name', '氢储能系统运行状态', 'Position', [300, 300, 1200, 600]);

subplot(2, 1, 1);
% 使用已计算的日级别数据

% 创建日级别柱状图
day_vector = 1:n_complete_days;
bar_colors = zeros(n_complete_days, 3);
for i = 1:n_complete_days
    if daily_hydrogen_power(i) > 0
        bar_colors(i, :) = [0.8, 0.4, 0.2];  % 用氢：橙色
    elseif daily_hydrogen_power(i) < 0
        bar_colors(i, :) = [0.2, 0.6, 0.8];  % 制氢：淡蓝色
    else
        bar_colors(i, :) = [0.7, 0.7, 0.7];  % 停机：灰色
    end
end

bar(day_vector, daily_hydrogen_power, 'FaceColor', 'flat', 'CData', bar_colors, 'EdgeColor', 'none');
hold on;
plot([1, n_complete_days], [0, 0], 'k-', 'LineWidth', 1);  % 零线
title('氢储能系统日功率分布', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('日功率 (MWh)');
xlabel('时间 (天)');
grid on;
legend('用氢(+) / 制氢(-)', 'Location', 'best');

% 添加文本说明
text(0.02, 0.95, sprintf('电解槽效率: %.1f%%', electrolyzer_efficiency*100), ...
    'Units', 'normalized', 'FontSize', 10, 'BackgroundColor', 'white');
text(0.02, 0.88, sprintf('燃料电池效率: %.1f%%', fuel_cell_efficiency*100), ...
    'Units', 'normalized', 'FontSize', 10, 'BackgroundColor', 'white');

subplot(2, 1, 2);
% 累积氢储量变化
time_days_storage = time_vector / 24;  % 将小时转换为天
plot(time_days_storage, hydrogen_cumsum, 'Color', [0.2, 0.6, 0.8], 'LineWidth', 2);
title('氢储罐储量变化（365天）', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('氢储罐存量 (MWh)');
xlabel('时间 (天)');
xlim([1, 365]);  % 固定显示365天
grid on;
legend('氢储罐存量', 'Location', 'best');

% 氢储能SOC分析
figure('Name', '氢储能SOC变化', 'Position', [400, 400, 1200, 400]);

% 计算SOC (State of Charge)
hydrogen_soc = hydrogen_cumsum / required_capacity * 100;

% 转换为天为单位的时间轴
time_days = time_vector / 24;  % 将小时转换为天

plot(time_days, hydrogen_soc, 'Color', [0.2, 0.6, 0.8], 'LineWidth', 2);
hold on;

% 添加安全运行区间
fill([time_days(1), time_days(end), time_days(end), time_days(1)], ...
     [20, 20, 80, 80], [0.9, 0.9, 0.9], 'FaceAlpha', 0.3, 'EdgeColor', 'none');

% 添加安全线
plot([time_days(1), time_days(end)], [20, 20], 'r--', 'LineWidth', 1, 'DisplayName', '最低SOC');
plot([time_days(1), time_days(end)], [80, 80], 'r--', 'LineWidth', 1, 'DisplayName', '最高SOC');


title('氢储能系统SOC变化（365天）', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('SOC (%)');
xlabel('时间 (天)');
ylim([0, 100]);
xlim([1, 365]);  % 固定显示365天
grid on;
legend('SOC', '安全运行区', '最低SOC (20%)', '最高SOC (80%)',  'Location', 'best');

% SOC统计分析
min_soc = min(hydrogen_soc);
max_soc = max(hydrogen_soc);
avg_soc = mean(hydrogen_soc);
soc_range = max_soc - min_soc;

% 安全运行分析
low_soc_hours = sum(hydrogen_soc < 20);  % SOC低于20%的小时数
high_soc_hours = sum(hydrogen_soc > 80); % SOC高于80%的小时数
optimal_soc_hours = sum(hydrogen_soc >= 20 & hydrogen_soc <= 80); % 安全运行小时数

fprintf('\n=== 氢储能SOC分析 ===\n');
fprintf('最低SOC: %.1f%%\n', min_soc);
fprintf('最高SOC: %.1f%%\n', max_soc);
fprintf('平均SOC: %.1f%%\n', avg_soc);
fprintf('SOC波动范围: %.1f%%\n', soc_range);
fprintf('低SOC运行时间: %d 小时 (%.1f%%) - SOC < 20%%\n', low_soc_hours, low_soc_hours/length(hydrogen_soc)*100);
fprintf('高SOC运行时间: %d 小时 (%.1f%%) - SOC > 80%%\n', high_soc_hours, high_soc_hours/length(hydrogen_soc)*100);
fprintf('安全运行时间: %d 小时 (%.1f%%) - 20%% ≤ SOC ≤ 80%%\n', optimal_soc_hours, optimal_soc_hours/length(hydrogen_soc)*100);

%% 计算相邻两天的ΔSOC（调度约束计算）
fprintf('\n=== 氢储能调度约束分析 ===\n');

% 计算相邻两天的ΔSOC（用日功率分布）
delta_soc = zeros(n_complete_days-1, 1);
for day = 1:n_complete_days-1
    delta_soc(day) = daily_hydrogen_power(day+1) - daily_hydrogen_power(day);
end

% 统计ΔSOC特征
max_positive_delta = max(delta_soc);
max_negative_delta = min(delta_soc);
avg_delta = mean(abs(delta_soc));
delta_std = std(delta_soc);

fprintf('最大正ΔSOC: %.2f MWh (需要额外制氢)\n', max_positive_delta);
fprintf('最大负ΔSOC: %.2f MWh (需要额外用氢)\n', max_negative_delta);
fprintf('平均|ΔSOC|: %.2f MWh\n', avg_delta);
fprintf('ΔSOC标准差: %.2f MWh\n', delta_std);

% 分析ΔSOC分布
positive_deltas = sum(delta_soc > 0);
negative_deltas = sum(delta_soc < 0);
zero_deltas = sum(abs(delta_soc) < 1);  % 基本平衡

fprintf('需要额外制氢天数: %d 天 (%.1f%%)\n', positive_deltas, positive_deltas/(n_complete_days-1)*100);
fprintf('需要额外用氢天数: %d 天 (%.1f%%)\n', negative_deltas, negative_deltas/(n_complete_days-1)*100);
fprintf('基本平衡天数: %d 天 (%.1f%%)\n', zero_deltas, zero_deltas/(n_complete_days-1)*100);

% 绘制ΔSOC调度约束图
figure('Name', '氢储能调度约束ΔSOC', 'Position', [500, 500, 1200, 400]);

day_vector_delta = 1:n_complete_days-1;
bar_colors_delta = zeros(length(delta_soc), 3);
for i = 1:length(delta_soc)
    if delta_soc(i) > 0
        bar_colors_delta(i, :) = [0.8, 0.4, 0.2];  % 需要额外制氢：橙色
    elseif delta_soc(i) < 0
        bar_colors_delta(i, :) = [0.2, 0.6, 0.8];  % 需要额外用氢：蓝色
    else
        bar_colors_delta(i, :) = [0.7, 0.7, 0.7];  % 平衡：灰色
    end
end

bar(day_vector_delta, delta_soc, 'FaceColor', 'flat', 'CData', bar_colors_delta, 'EdgeColor', 'none');
hold on;
plot([1, length(delta_soc)], [0, 0], 'k-', 'LineWidth', 1);  % 零线

title('相邻两天氢储能ΔSOC变化（调度约束）', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('ΔSOC (MWh)');
xlabel('时间 (天)');
grid on;
legend('ΔSOC：额外制氢(+) / 额外用氢(-)', 'Location', 'best');

% 添加说明文本
text(0.02, 0.95, '调度策略：每日氢储能充放电差值必须等于ΔSOC', ...
    'Units', 'normalized', 'FontSize', 10, 'BackgroundColor', 'yellow', 'FontWeight', 'bold');
text(0.02, 0.88, sprintf('配合电池系统实现功率完全平衡'), ...
    'Units', 'normalized', 'FontSize', 10, 'BackgroundColor', 'white');

%% 基于最小化运行成本的电池储能优化
fprintf('\n=== 基于最小化运行成本的多储能协调优化 ===\n');
drawnow;  % 刷新显示

% 储能系统参数设置
battery_efficiency_charge = 0.95;      % 电池充电效率
battery_efficiency_discharge = 0.95;   % 电池放电效率
battery_capacity = 1000;               % 电池容量 (MWh)
battery_max_power = 200;               % 电池最大功率 (MW)

% 成本参数设置 (元/MWh)
cost_battery_cycle = 100;              % 电池循环成本
cost_hydrogen_production = 200;        % 制氢成本
cost_hydrogen_consumption = 150;       % 用氢成本
cost_battery_degradation = 50;         % 电池老化成本

fprintf('储能系统参数:\n');
fprintf('电池充电效率: %.1f%%\n', battery_efficiency_charge*100);
fprintf('电池放电效率: %.1f%%\n', battery_efficiency_discharge*100);
fprintf('电池容量: %.0f MWh\n', battery_capacity);
fprintf('电池最大功率: %.0f MW\n', battery_max_power);

% 初始化优化变量
n_hours = length(trend);
battery_power_opt = zeros(n_hours, 1);    % 电池功率 (MW)
hydrogen_power_opt = zeros(n_hours, 1);   % 氢储能功率 (MW)
battery_soc = zeros(n_hours, 1);          % 电池SOC

% 使用简化的启发式优化算法
fprintf('\n开始简化启发式优化调度...\n');

total_cost = 0;
optimization_results = [];

for day = 1:n_complete_days
    if mod(day, 50) == 0
        fprintf('正在优化第 %d/%d 天...\n', day, n_complete_days);
    end
    
    day_start = (day - 1) * 24 + 1;
    day_end = min(day * 24, n_hours);
    daily_hours = day_end - day_start + 1;
    
    % 当天净负荷趋势
    P_net = trend(day_start:day_end);
    
    % 氢储能日约束（ΔSOC约束）
        target_daily_hydrogen = daily_hydrogen_power(day);

    % 启发式分配策略
    % 1. 氢储能优先承担长期趋势（满足ΔSOC约束）
    % 2. 电池承担短期波动
    
    % 计算氢储能功率（满足日约束）
    hydrogen_temp = zeros(daily_hours, 1);
    
    if abs(target_daily_hydrogen) > 0.1
        % 根据净负荷大小分配氢储能功率
        net_positive = P_net(P_net > 0);
        net_negative = P_net(P_net < 0);
        
        if target_daily_hydrogen > 0 && ~isempty(net_positive)
            % 需要净用氢，在正负荷时段用氢
            positive_indices = find(P_net > 0);
            total_positive = sum(P_net(positive_indices));
            if total_positive > 0
                for i = 1:length(positive_indices)
                    idx = positive_indices(i);
                    hydrogen_temp(idx) = target_daily_hydrogen * P_net(idx) / total_positive;
                end
            end
        elseif target_daily_hydrogen < 0 && ~isempty(net_negative)
            % 需要净制氢，在负负荷时段制氢
            negative_indices = find(P_net < 0);
            total_negative = sum(abs(P_net(negative_indices)));
            if total_negative > 0
                for i = 1:length(negative_indices)
                    idx = negative_indices(i);
                    hydrogen_temp(idx) = target_daily_hydrogen * abs(P_net(idx)) / total_negative;
                end
            end
        end
        
        % 如果分配不足，平均分配剩余部分
        current_sum = sum(hydrogen_temp);
        if abs(current_sum - target_daily_hydrogen) > 0.1
            remaining = target_daily_hydrogen - current_sum;
            hydrogen_temp = hydrogen_temp + remaining / daily_hours;
        end
    end
    
    % 限制氢储能功率在合理范围内
    max_h2_power = max(abs(P_net)) * 1.2;
    hydrogen_temp = max(-max_h2_power, min(max_h2_power, hydrogen_temp));
    
    % 电池承担剩余功率
    battery_temp = P_net - hydrogen_temp;
    
    % 限制电池功率
    battery_temp = max(-battery_max_power, min(battery_max_power, battery_temp));
    
    % 如果电池功率超限，调整氢储能
    if any(abs(battery_temp) > battery_max_power)
        excess_indices = find(abs(battery_temp) > battery_max_power);
        for idx = excess_indices'
            if battery_temp(idx) > battery_max_power
                excess = battery_temp(idx) - battery_max_power;
                hydrogen_temp(idx) = hydrogen_temp(idx) + excess;
                battery_temp(idx) = battery_max_power;
            elseif battery_temp(idx) < -battery_max_power
                excess = battery_temp(idx) + battery_max_power;
                hydrogen_temp(idx) = hydrogen_temp(idx) + excess;
                battery_temp(idx) = -battery_max_power;
            end
        end
    end
    
    % 强制功率平衡（确保精度为0）
    power_sum = hydrogen_temp + battery_temp;
    balance_error = P_net - power_sum;
    
    % 将平衡误差分配给电池（因为电池响应更快）
    battery_temp = battery_temp + balance_error;
    
    % 再次检查电池功率限制
    battery_exceed = find(abs(battery_temp) > battery_max_power);
    if ~isempty(battery_exceed)
        for idx = battery_exceed'
            if battery_temp(idx) > battery_max_power
                excess = battery_temp(idx) - battery_max_power;
                hydrogen_temp(idx) = hydrogen_temp(idx) + excess;
                battery_temp(idx) = battery_max_power;
            elseif battery_temp(idx) < -battery_max_power
                excess = battery_temp(idx) + battery_max_power;
                hydrogen_temp(idx) = hydrogen_temp(idx) + excess;
                battery_temp(idx) = -battery_max_power;
            end
        end
    end
    
    % 保存优化结果
    hydrogen_power_opt(day_start:day_end) = hydrogen_temp;
    battery_power_opt(day_start:day_end) = battery_temp;
    
    % 计算成本
    daily_battery_energy = sum(abs(battery_temp));
    daily_hydrogen_energy = sum(abs(hydrogen_temp));
    
    daily_battery_cost = daily_battery_energy * cost_battery_cycle;
    daily_hydrogen_production_cost = sum(abs(hydrogen_temp(hydrogen_temp < 0))) * cost_hydrogen_production;
    daily_hydrogen_consumption_cost = sum(hydrogen_temp(hydrogen_temp > 0)) * cost_hydrogen_consumption;
    
    daily_cost = daily_battery_cost + daily_hydrogen_production_cost + daily_hydrogen_consumption_cost;
    total_cost = total_cost + daily_cost;
    
    % 记录优化结果
    optimization_results = [optimization_results; 
                           day, daily_cost, daily_battery_energy, daily_hydrogen_energy];
end

% 计算电池SOC
battery_soc(1) = 50;  % 初始SOC 50%
for t = 2:n_hours
    if battery_power_opt(t) > 0  % 放电
        energy_change = -battery_power_opt(t) / battery_efficiency_discharge;
    else  % 充电
        energy_change = -battery_power_opt(t) * battery_efficiency_charge;
    end
    battery_soc(t) = battery_soc(t-1) + energy_change / battery_capacity * 100;
    battery_soc(t) = max(0, min(100, battery_soc(t)));  % 限制在0-100%
end

% 验证功率平衡
power_balance_error_opt = trend - hydrogen_power_opt - battery_power_opt;
max_balance_error_opt = max(abs(power_balance_error_opt));

fprintf('\n=== 优化结果统计 ===\n');
fprintf('总运行成本: %.2f 万元\n', total_cost/10000);
fprintf('平均日运行成本: %.2f 元\n', total_cost/n_complete_days);
fprintf('功率平衡误差: %.6f MW\n', max_balance_error_opt);
fprintf('电池最大功率: %.2f MW\n', max(abs(battery_power_opt)));
fprintf('氢储能最大功率: %.2f MW\n', max(abs(hydrogen_power_opt)));
fprintf('电池SOC范围: %.1f%% ~ %.1f%%\n', min(battery_soc), max(battery_soc));

% 计算运行特性
battery_cycle_count = sum(abs(diff(battery_power_opt > 0))) / 2;
hydrogen_work_hours_opt = sum(abs(hydrogen_power_opt) > 1);
battery_work_hours_opt = sum(abs(battery_power_opt) > 1);

fprintf('电池循环次数: %.0f 次\n', battery_cycle_count);
fprintf('氢储能工作时间: %d 小时 (%.1f%%)\n', hydrogen_work_hours_opt, hydrogen_work_hours_opt/n_hours*100);
fprintf('电池工作时间: %d 小时 (%.1f%%)\n', battery_work_hours_opt, battery_work_hours_opt/n_hours*100);

%% 绘制优化后的功率平衡图（与用户图片一致的样式）
% 主功率平衡图
figure('Name', '氢储能+电池功率平衡', 'Position', [100, 100, 1400, 800]);

% 转换为天为单位显示
time_days_balance = time_vector / 24;

% 第一层：净负荷趋势分量
subplot(3, 1, 1);
plot(time_days_balance, trend, 'Color', [0.2, 0.6, 0.8], 'LineWidth', 1.5);
title('净负荷趋势分量', 'FontSize', 14, 'FontWeight', 'bold');
ylabel('功率 (MW)', 'FontSize', 12);
xlim([0, 365]);
ylim([min(trend)*1.1, max(trend)*1.1]);
grid on;
box on;
legend('净负荷趋势', 'Location', 'northeast', 'FontSize', 10);

% 第二层：氢储能与电池功率分配
subplot(3, 1, 2);
plot(time_days_balance, hydrogen_power_opt, 'Color', [0.2, 0.6, 0.8], 'LineWidth', 1.5, 'DisplayName', '氢储能功率');
hold on;
plot(time_days_balance, battery_power_opt, 'Color', [0.8, 0.4, 0.2], 'LineWidth', 1.5, 'DisplayName', '电池功率');
plot(time_days_balance, trend, 'k--', 'LineWidth', 1, 'DisplayName', '净负荷趋势');
title('氢储能与电池功率分配', 'FontSize', 14, 'FontWeight', 'bold');
ylabel('功率 (MW)', 'FontSize', 12);
xlim([0, 365]);
ylim([min([hydrogen_power_opt; battery_power_opt; trend])*1.1, max([hydrogen_power_opt; battery_power_opt; trend])*1.1]);
grid on;
box on;
legend('Location', 'northeast', 'FontSize', 10);

% 第三层：功率平衡堆叠图
subplot(3, 1, 3);

% 创建堆叠柱状图数据
hydrogen_data = hydrogen_power_opt';
battery_data = battery_power_opt';

% 绘制堆叠柱状图
h1 = bar(time_days_balance, hydrogen_data, 'FaceColor', [0.2, 0.6, 0.8], 'EdgeColor', 'none', 'BarWidth', 1);
hold on;
h2 = bar(time_days_balance, battery_data, 'FaceColor', [0.8, 0.4, 0.2], 'EdgeColor', 'none', 'BarWidth', 1);

% 叠加净负荷趋势线
plot(time_days_balance, trend, 'k-', 'LineWidth', 2);

% 添加功率平衡精度标签（绿色背景）
if max_balance_error_opt < 1e-10
    balance_text = '功率平衡精度: ±0.000000 MW';
else
    balance_text = sprintf('功率平衡精度: ±%.6f MW', max_balance_error_opt);
end
text(0.02, 0.95, balance_text, ...
    'Units', 'normalized', 'FontSize', 11, 'BackgroundColor', [0.2, 0.8, 0.2], ...
    'Color', 'white', 'FontWeight', 'bold', 'EdgeColor', 'black');

title('功率平衡堆叠图（氢储能 + 电池 = 净负荷）', 'FontSize', 14, 'FontWeight', 'bold');
ylabel('功率 (MW)', 'FontSize', 12);
xlabel('时间 (天)', 'FontSize', 12);
xlim([0, 365]);
ylim([min([hydrogen_power_opt; battery_power_opt; trend])*1.1, max([hydrogen_power_opt; battery_power_opt; trend])*1.1]);
grid on;
box on;
legend('氢储能', '电池', '净负荷趋势', 'Location', 'northeast', 'FontSize', 10);

% 成本优化效果分析图
figure('Name', '成本优化效果分析', 'Position', [700, 700, 1200, 800]);

% 日成本分析
if ~isempty(optimization_results)
    subplot(2, 2, 1);
    plot(optimization_results(:,1), optimization_results(:,2), 'Color', [0.2, 0.6, 0.8], 'LineWidth', 1.5);
    title('日运行成本变化', 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('天数');
    ylabel('日成本 (元)');
    grid on;
    
    subplot(2, 2, 2);
    plot(optimization_results(:,1), optimization_results(:,3), 'Color', [0.8, 0.4, 0.2], 'LineWidth', 1.5, 'DisplayName', '电池');
    hold on;
    plot(optimization_results(:,1), optimization_results(:,4), 'Color', [0.2, 0.6, 0.8], 'LineWidth', 1.5, 'DisplayName', '氢储能');
    title('日储能利用量', 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('天数');
    ylabel('能量 (MWh)');
    legend('Location', 'best');
    grid on;
    
    % 成本构成分析
    subplot(2, 2, 3);
    daily_battery_cost = optimization_results(:,3) * cost_battery_cycle;
    daily_hydrogen_cost = optimization_results(:,4) * cost_hydrogen_consumption;
    cost_data = [daily_battery_cost, daily_hydrogen_cost];
    bar(optimization_results(:,1), cost_data, 'stacked');
    title('日成本构成分析', 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('天数');
    ylabel('成本 (元)');
    legend('电池成本', '氢储能成本', 'Location', 'best');
    grid on;
    
    % 储能利用率对比
    subplot(2, 2, 4);
    battery_utilization = optimization_results(:,3) / (battery_max_power * 24) * 100;
    hydrogen_utilization = optimization_results(:,4) / (max(abs(trend)) * 24) * 100;
    plot(optimization_results(:,1), battery_utilization, 'Color', [0.8, 0.4, 0.2], 'LineWidth', 1.5, 'DisplayName', '电池利用率');
    hold on;
    plot(optimization_results(:,1), hydrogen_utilization, 'Color', [0.2, 0.6, 0.8], 'LineWidth', 1.5, 'DisplayName', '氢储能利用率');
    title('储能系统利用率', 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('天数');
    ylabel('利用率 (%)');
    legend('Location', 'best');
    grid on;
end

%% 7. 结果保存
fprintf('保存分解结果...\n');

% 保存数值结果
results_table = table(time_vector, data, trend, detrended, hydrogen_power, hydrogen_cumsum, hydrogen_soc, ...
    'VariableNames', {'时间', '原始数据', '趋势分量', '去趋势分量', '氢储能功率', '氢储罐存量', '氢储能SOC'});

writetable(results_table, 'STL分解结果.xlsx');

% 保存典型日模式
daily_pattern_table = table((1:daily_period)', daily_pattern, daily_pattern_count, ...
    'VariableNames', {'小时', '典型日净负荷偏差', '样本数量'});
writetable(daily_pattern_table, '典型日净负荷模式.xlsx');

% 保存氢储能分析结果
hydrogen_analysis_table = table(time_vector, trend, hydrogen_power, hydrogen_storage_change, hydrogen_type, hydrogen_cumsum, hydrogen_soc, ...
    'VariableNames', {'时间', '净负荷趋势', '氢储能功率', '氢储量变化', '运行模式', '氢储罐存量', '氢储能SOC'});
writetable(hydrogen_analysis_table, '氢储能系统分析.xlsx');

% 保存调度约束ΔSOC分析结果
delta_soc_table = table((1:n_complete_days-1)', delta_soc, ...
    'VariableNames', {'天数', 'ΔSOC约束'});
writetable(delta_soc_table, '氢储能调度约束ΔSOC.xlsx');

% 保存功率平衡分析结果（原始）
power_balance_table = table(time_vector, trend, hydrogen_power, battery_power_opt, power_balance_error_opt, ...
    'VariableNames', {'时间', '净负荷趋势', '氢储能功率', '电池功率', '平衡误差'});
writetable(power_balance_table, '功率平衡分析.xlsx');

% 保存优化结果
optimization_table = table(time_vector, trend, hydrogen_power_opt, battery_power_opt, battery_soc, power_balance_error_opt, ...
    'VariableNames', {'时间', '净负荷趋势', '氢储能功率优化', '电池功率优化', '电池SOC', '功率平衡误差'});
writetable(optimization_table, '最小化成本优化结果.xlsx');

% 保存日优化统计结果
if ~isempty(optimization_results)
    daily_optimization_table = table(optimization_results(:,1), optimization_results(:,2), ...
        optimization_results(:,3), optimization_results(:,4), ...
        'VariableNames', {'天数', '日成本', '电池日利用量', '氢储能日利用量'});
    writetable(daily_optimization_table, '日优化统计结果.xlsx');
end

% 保存成本分析汇总
cost_summary = table(total_cost, total_cost/n_complete_days, max_balance_error_opt, ...
    max(abs(battery_power_opt)), max(abs(hydrogen_power_opt)), ...
    min(battery_soc), max(battery_soc), battery_cycle_count, ...
    hydrogen_work_hours_opt, battery_work_hours_opt, ...
    'VariableNames', {'总成本', '平均日成本', '最大平衡误差', '电池最大功率', '氢储能最大功率', ...
    '最低电池SOC', '最高电池SOC', '电池循环次数', '氢储能工作小时', '电池工作小时'});
writetable(cost_summary, '成本优化汇总.xlsx');

fprintf('分解完成！结果已保存到文件。\n');
fprintf('典型日模式已保存到：典型日净负荷模式.xlsx\n');
fprintf('氢储能分析已保存到：氢储能系统分析.xlsx\n');
fprintf('调度约束已保存到：氢储能调度约束ΔSOC.xlsx\n');
fprintf('最小化成本优化结果已保存到：最小化成本优化结果.xlsx\n');
fprintf('日优化统计已保存到：日优化统计结果.xlsx\n');
fprintf('成本优化汇总已保存到：成本优化汇总.xlsx\n');

%% ====================================================================
%% 辅助函数定义
%% ====================================================================

%% 季节性分量提取函数
function seasonal = extract_seasonal_component(data, period, window, weights)
    n = length(data);
    
    % 扩展数据以处理边界
    n_cycles = ceil(n / period);
    extended_data = [data; zeros(n_cycles * period - n, 1)];
    extended_weights = [weights; ones(n_cycles * period - n, 1)];
    
    seasonal_extended = zeros(size(extended_data));
    
    % 对每个季节位置进行平滑
    for pos = 1:period
        indices = pos:period:length(extended_data);
        if length(indices) > 1
            seasonal_extended(indices) = loess_smooth(extended_data(indices), ...
                window, 1, extended_weights(indices));
        else
            seasonal_extended(indices) = extended_data(indices);
        end
    end
    
    % 低通滤波去除趋势
    seasonal_raw = seasonal_extended(1:n);
    seasonal_smooth = moving_average(seasonal_raw, 3);
    seasonal_trend = loess_smooth(seasonal_smooth, period, 1, weights);
    
    % 去趋势得到纯季节性分量
    seasonal = seasonal_raw - seasonal_trend;
end

%% LOESS局部回归平滑函数
function smoothed = loess_smooth(data, window, degree, weights)
    n = length(data);
    smoothed = zeros(size(data));
    
    for i = 1:n
        % 确定局部窗口
        half_window = floor(window / 2);
        left_bound = max(1, i - half_window);
        right_bound = min(n, i + half_window);
        
        % 构建局部回归
        local_x = (left_bound:right_bound)';
        local_y = data(left_bound:right_bound);
        local_weights = weights(left_bound:right_bound);
        
        % 距离权重
        distances = abs(local_x - i);
        max_dist = max(distances);
        if max_dist > 0
            tri_weights = max(0, 1 - distances / max_dist);
        else
            tri_weights = ones(size(distances));
        end
        
        % 综合权重
        combined_weights = local_weights .* tri_weights;
        
        % 加权最小二乘拟合
        if degree == 0
            % 加权平均
            smoothed(i) = sum(local_y .* combined_weights) / sum(combined_weights);
        else
            % 加权线性回归
            X = [(local_x - i).^0, (local_x - i).^1];
            W = diag(combined_weights);
            
            try
                beta = (X' * W * X) \ (X' * W * local_y);
                smoothed(i) = beta(1);  % 在i点的预测值
            catch
                % 如果矩阵奇异，使用加权平均
                smoothed(i) = sum(local_y .* combined_weights) / sum(combined_weights);
            end
        end
    end
end

%% Bisquare权重函数
function w = bisquare_weights(u)
    abs_u = abs(u);
    w = zeros(size(u));
    valid = abs_u < 1;
    w(valid) = (1 - abs_u(valid).^2).^2;
end

%% 移动平均函数
function smoothed = moving_average(data, window)
    n = length(data);
    smoothed = zeros(size(data));
    half_window = floor(window / 2);
    
    for i = 1:n
        left = max(1, i - half_window);
        right = min(n, i + half_window);
        smoothed(i) = mean(data(left:right));
    end
end 

%% 成本计算函数
function cost = calculate_total_cost(battery_power, hydrogen_power, cost_params)
    % 输入参数：
    % battery_power: 电池功率时间序列
    % hydrogen_power: 氢储能功率时间序列
    % cost_params: 成本参数结构体
    
    % 电池成本计算
    battery_energy = sum(abs(battery_power));
    battery_cost = battery_energy * cost_params.battery_cycle;
    
    % 氢储能成本计算
    hydrogen_production = sum(hydrogen_power(hydrogen_power < 0));  % 制氢
    hydrogen_consumption = sum(hydrogen_power(hydrogen_power > 0)); % 用氢
    
    hydrogen_cost = abs(hydrogen_production) * cost_params.hydrogen_production + ...
                   hydrogen_consumption * cost_params.hydrogen_consumption;
    
    % 总成本
    cost = battery_cost + hydrogen_cost;
end 